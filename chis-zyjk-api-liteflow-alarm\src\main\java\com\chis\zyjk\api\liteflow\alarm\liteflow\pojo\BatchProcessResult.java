package com.chis.zyjk.api.liteflow.alarm.liteflow.pojo;

import com.chis.project.frame.common.tools.json.JSONObject;
import com.chis.zyjk.api.liteflow.alarm.pojo.po.AlertRecordNoticeLogPO;
import com.chis.zyjk.api.liteflow.alarm.pojo.po.AlertRecordPO;
import com.chis.zyjk.api.liteflow.alarm.pojo.po.AlertRecordLogPO;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量处理结果类
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Data
public class BatchProcessResult {
    private int inputCount;
    private int successCount;
    private int createdCount;
    private int updatedCount;
    private int disposedCount;
    private int ignoredCount;
    private int errorCount;
    private long executionTime;
    private List<String> records = new ArrayList<>();

    // 分类存储不同动作的记录
    private List<AlertRecordPO> createList = new ArrayList<>();      // 新增主表记录
    private List<AlertRecordPO> updateList = new ArrayList<>();      // 更新主表记录
    private List<AlertRecordPO> disposeList = new ArrayList<>();     // 处置主表记录
    private List<AlertRecordLogPO> logList = new ArrayList<>();      // 所有子表记录
    private List<AlertRecordNoticeLogPO> noticeLogList = new ArrayList<>();      // 所有通知子表记录

    // 兼容原有逻辑
    private List<AlertRecordPO> alertRecords = new ArrayList<>();
    private List<JSONObject> errorLogs = new ArrayList<>();

}
