package com.chis.zyjk.api.liteflow.alarm.liteflow.executor;

import com.chis.project.frame.common.tools.core.util.StrUtil;
import com.chis.project.frame.common.tools.json.JSONObject;
import com.yomahub.liteflow.builder.el.LiteFlowChainELBuilder;
import com.chis.project.frame.common.tools.json.JSONUtil;
import com.chis.zyjk.api.liteflow.alarm.liteflow.common.NodeConfigHelper;
import com.chis.zyjk.api.liteflow.alarm.liteflow.common.NodeDataHelper;
import com.chis.zyjk.api.liteflow.alarm.liteflow.exception.LiteFlowErrorCode;
import com.chis.zyjk.api.liteflow.alarm.liteflow.exception.LiteFlowExceptionHelper;
import com.chis.zyjk.api.liteflow.alarm.liteflow.exception.LiteFlowRuleException;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import com.yomahub.liteflow.slot.DefaultContext;
import com.yomahub.liteflow.slot.Slot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * LiteFlow规则执行器
 * 负责加载规则配置、执行LiteFlow表达式、管理执行上下文
 *
 * <AUTHOR> Assistant
 * @since 2024-01-17
 */
@Slf4j
@Service
public class LiteFlowRuleExecutor<T> {

    @Autowired
    private FlowExecutor flowExecutor;

    /**
     * 执行LiteFlow规则（纯动态配置模式）
     *
     * @param expression    LiteFlow表达式
     * @param globalContext 全局上下文
     * @param nodeConfigs   节点配置映射
     * @return 执行结果
     */
    public RuleExecutionResult executeRule(String expression, Map<String, Object> globalContext,
                                           Map<String, JSONObject> nodeConfigs) {
        log.info("开始执行LiteFlow规则，expression: {}", expression);

        long startTime = System.currentTimeMillis();
        RuleExecutionResult result = new RuleExecutionResult();
        result.setRuleCode("DYNAMIC_EXPRESSION");
        result.setStartTime(new Date(startTime));

        try {
            // 1. 验证表达式
            if (expression == null || expression.trim().isEmpty()) {
                throw LiteFlowExceptionHelper.createRuleException(
                        LiteFlowErrorCode.RULE_EXPRESSION_EMPTY,
                        "LiteFlow表达式不能为空"
                );
            }

            // 2. 验证动态节点配置（必须提供）
            if (nodeConfigs == null || nodeConfigs.isEmpty()) {
                throw LiteFlowExceptionHelper.createRuleException(
                        LiteFlowErrorCode.RULE_NODE_CONFIG_EMPTY,
                        "必须提供nodeConfigs配置"
                );
            }

            // 3. 初始化执行上下文
            DefaultContext context = initializeContext("DYNAMIC_EXPRESSION", globalContext);

            // 4. 初始化配置缓存到Context中（避免并发串扰）
            NodeConfigHelper.initializeConfigCache(context, nodeConfigs);

            // 4. 动态创建并执行LiteFlow表达式
            String chainId = "DYNAMIC_CHAIN_" + System.currentTimeMillis();

            // 动态创建chain
            LiteFlowChainELBuilder.createChain()
                    .setChainId(chainId)
                    .setEL(expression)
                    .build();

            // 执行动态创建的chain
            LiteflowResponse response = flowExecutor.execute2Resp(chainId, null, context);

            // 5. 处理执行结果
            processExecutionResult(response, result);

            result.setSuccess(true);
            result.setMessage("规则执行成功");

            log.info("LiteFlow规则执行完成，expression: {}, 耗时: {}ms",
                    expression, System.currentTimeMillis() - startTime);

        } catch (LiteFlowRuleException e) {
            // 如果已经是LiteFlow规则异常，直接处理
            log.error("LiteFlow规则异常: {}", e.getDetailedMessage(), e);
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            result.setException(e);
        } catch (Exception e) {
            // 包装为LiteFlow规则异常
            LiteFlowRuleException ruleException = LiteFlowExceptionHelper.wrapRuleException(
                    e,
                    LiteFlowErrorCode.RULE_EXECUTION_FAILED,
                    "规则执行失败: " + e.getMessage(),
                    expression
            );
            result.setSuccess(false);
            result.setMessage(ruleException.getMessage());
            result.setException(ruleException);
        } finally {
            result.setEndTime(new Date());
            result.setExecutionTime(System.currentTimeMillis() - startTime);
        }

        return result;
    }

    /**
     * 初始化执行上下文
     *
     * @param ruleCode   规则编码
     * @param contextMap 上下文Map
     * @return 执行上下文
     */
    private DefaultContext initializeContext(String ruleCode, Map<String, T> contextMap) {
        DefaultContext context = new DefaultContext();

        if (contextMap != null) {
            for (Map.Entry<String, T> entry : contextMap.entrySet()) {
                context.setData(entry.getKey(), entry.getValue());
            }
        }

        log.debug("初始化执行上下文完成，ruleCode: {}", ruleCode);
        return context;
    }

    /**
     * 处理执行结果
     *
     * @param response LiteFlow响应
     * @param result   执行结果
     */
    private void processExecutionResult(LiteflowResponse response, RuleExecutionResult result) {
        if (response == null) {
            throw new RuntimeException("LiteFlow执行响应为空");
        }

        if (!response.isSuccess()) {
            throw new RuntimeException("LiteFlow执行失败: " + response.getMessage());
        }

        // 提取执行结果数据
        DefaultContext context = response.getFirstContextBean();
        if (context != null) {
            // 提取预警记录
            Object alertRecords = context.getData("alertRecords");
            if (alertRecords != null) {
                result.setAlertRecords(alertRecords);
            }

            // 提取通知结果
            Object notificationResults = context.getData("notificationResults");
            if (notificationResults != null) {
                result.setNotificationResults(notificationResults);
            }

            // 提取处理统计
            Object processStats = context.getData("processStats");
            if (processStats != null) {
                result.setProcessStats(processStats);
            }
        }

        log.debug("处理执行结果完成");
    }

    /**
     * 规则配置类
     */
    public static class RuleConfig {
        private String ruleCode;
        private String ruleName;
        private String categoryCode;
        private String dedupKeyExpression;
        private String liteFlowExpression;
        private Map<String, String> nodeConfigs;

        // Getters and Setters
        public String getRuleCode() {
            return ruleCode;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

        public String getRuleName() {
            return ruleName;
        }

        public void setRuleName(String ruleName) {
            this.ruleName = ruleName;
        }

        public String getCategoryCode() {
            return categoryCode;
        }

        public void setCategoryCode(String categoryCode) {
            this.categoryCode = categoryCode;
        }

        public String getDedupKeyExpression() {
            return dedupKeyExpression;
        }

        public void setDedupKeyExpression(String dedupKeyExpression) {
            this.dedupKeyExpression = dedupKeyExpression;
        }

        public String getLiteFlowExpression() {
            return liteFlowExpression;
        }

        public void setLiteFlowExpression(String liteFlowExpression) {
            this.liteFlowExpression = liteFlowExpression;
        }

        public Map<String, String> getNodeConfigs() {
            return nodeConfigs;
        }

        public void setNodeConfigs(Map<String, String> nodeConfigs) {
            this.nodeConfigs = nodeConfigs;
        }
    }

    /**
     * 规则执行结果类
     */
    public static class RuleExecutionResult {
        private String ruleCode;
        private boolean success;
        private String message;
        private Date startTime;
        private Date endTime;
        private long executionTime;
        private Object alertRecords;
        private Object notificationResults;
        private Object processStats;
        private Exception exception;

        // Getters and Setters
        public String getRuleCode() {
            return ruleCode;
        }

        public void setRuleCode(String ruleCode) {
            this.ruleCode = ruleCode;
        }

        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Date getStartTime() {
            return startTime;
        }

        public void setStartTime(Date startTime) {
            this.startTime = startTime;
        }

        public Date getEndTime() {
            return endTime;
        }

        public void setEndTime(Date endTime) {
            this.endTime = endTime;
        }

        public long getExecutionTime() {
            return executionTime;
        }

        public void setExecutionTime(long executionTime) {
            this.executionTime = executionTime;
        }

        public Object getAlertRecords() {
            return alertRecords;
        }

        public void setAlertRecords(Object alertRecords) {
            this.alertRecords = alertRecords;
        }

        public Object getNotificationResults() {
            return notificationResults;
        }

        public void setNotificationResults(Object notificationResults) {
            this.notificationResults = notificationResults;
        }

        public Object getProcessStats() {
            return processStats;
        }

        public void setProcessStats(Object processStats) {
            this.processStats = processStats;
        }

        public Exception getException() {
            return exception;
        }

        public void setException(Exception exception) {
            this.exception = exception;
        }
    }
}
